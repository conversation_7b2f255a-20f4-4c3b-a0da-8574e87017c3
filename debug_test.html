<!DOCTYPE html>
<html>
<head>
    <title>URL正则测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>URL正则表达式测试</h1>
    
    <h2>测试用例1：</h2>
    <p id="test1"></p>
    
    <h2>测试用例2：</h2>
    <p id="test2"></p>
    
    <script>
        // 原始正则
        const originalRegex = new RegExp('https?://v\\.douyin\\.com/[A-Za-z0-9]+/?');
        
        // 修复后的正则
        const newRegex = new RegExp('https?://v\\.douyin\\.com/[^\\s]+');
        
        // URL清理函数
        function cleanExtractedUrl(url) {
            return url.replace(/[.,;!?\s]+$/, '');
        }
        
        // 测试用例
        const testCase1 = "9.71 复制打开抖音，看看【黄汉森的作品】来自1000米的后空翻 # 战术装备 # 热血青年... https://v.douyin.com/ggsCMPIVjb0/ 05/29 KWM:/ <EMAIL> ";
        const testCase2 = "8.25 复制打开抖音，看看【人民日报的作品】"你不会是金陵的孩子吧？""因为我是中华儿女啊，同... https://v.douyin.com/cQsNLoJ4-u8/ Wmd:/ 01/18 <EMAIL> ";
        
        function runTest(testCase, caseNum) {
            console.log(`\n=== 测试用例 ${caseNum} ===`);
            console.log('输入:', testCase);
            
            // 原始正则测试
            const originalMatch = testCase.match(originalRegex);
            console.log('原始正则匹配:', originalMatch ? originalMatch[0] : 'null');
            
            // 新正则测试
            const newMatch = testCase.match(newRegex);
            console.log('新正则匹配:', newMatch ? newMatch[0] : 'null');
            
            // URL清理测试
            if (newMatch) {
                const cleaned = cleanExtractedUrl(newMatch[0]);
                console.log('清理后:', cleaned);
            }
            
            // 显示结果
            const resultElement = document.getElementById(`test${caseNum}`);
            resultElement.innerHTML = `
                <strong>输入:</strong> ${testCase}<br>
                <strong>原始正则匹配:</strong> ${originalMatch ? originalMatch[0] : 'null'}<br>
                <strong>新正则匹配:</strong> ${newMatch ? newMatch[0] : 'null'}<br>
                <strong>清理后:</strong> ${newMatch ? cleanExtractedUrl(newMatch[0]) : 'null'}
            `;
        }
        
        // 运行测试
        runTest(testCase1, 1);
        runTest(testCase2, 2);
    </script>
</body>
</html>
