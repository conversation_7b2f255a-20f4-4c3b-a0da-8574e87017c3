<!DOCTYPE html>
<html>
<head>
    <title>URL提取测试</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>抖音URL提取测试</h1>
    
    <div>
        <h3>测试用例1:</h3>
        <textarea id="test1" style="width:100%; height:60px;">8.25 复制打开抖音，看看【人民日报的作品】"你不会是金陵的孩子吧？""因为我是中华儿女啊，同... https://v.douyin.com/cQsNLoJ4-u8/ Wmd:/ 01/18 <EMAIL></textarea>
        <button onclick="testExtraction('test1')">测试提取</button>
        <div id="result1"></div>
    </div>
    
    <div>
        <h3>测试用例2:</h3>
        <textarea id="test2" style="width:100%; height:60px;">9.71 复制打开抖音，看看【黄汉森的作品】来自1000米的后空翻 # 战术装备 # 热血青年... https://v.douyin.com/ggsCMPIVjb0/ 05/29 KWM:/ <EMAIL></textarea>
        <button onclick="testExtraction('test2')">测试提取</button>
        <div id="result2"></div>
    </div>

    <script>
        // 提取URL的正则表达式 - 支持任何非空白字符
        const urlRegex = new RegExp('https?://v\\.douyin\\.com/[^\\s]+');

        // URL清理函数 - 移除末尾的标点符号
        function cleanExtractedUrl(url) {
            return url.replace(/[.,;!?\s]+$/, '');
        }

        // 提取并验证URL
        function extractAndValidateUrl(input) {
            if (!input) {
                return null;
            }

            const match = input.match(urlRegex);
            if (match) {
                const cleanedUrl = cleanExtractedUrl(match[0]);
                console.log('提取到的URL:', match[0], '清理后:', cleanedUrl);
                return cleanedUrl;
            }

            if (input.startsWith('http')) {
                return cleanExtractedUrl(input);
            }

            return null;
        }

        function testExtraction(testId) {
            const input = $('#' + testId).val().trim();
            const result = extractAndValidateUrl(input);
            const resultDiv = $('#result' + testId.slice(-1));
            
            if (result) {
                resultDiv.html('<strong>提取结果:</strong> <span style="color:green;">' + result + '</span>');
            } else {
                resultDiv.html('<strong>提取失败:</strong> <span style="color:red;">未找到有效URL</span>');
            }
        }
    </script>
</body>
</html>
